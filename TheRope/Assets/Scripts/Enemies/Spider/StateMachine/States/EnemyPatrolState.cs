// Copyright Isto Inc.

using Isto.Core.StateMachine;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyPatrolState : EnemyState
    {
        // OTHER FIELDS

        private float _timer;

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);
            _timer = 0f;

            // // Set a random patrol target if we don't have one or if current target is a player
            // if (_spiderController.TargetTransform == null ||
            //     _spiderController.TargetTransform.CompareTag("Player"))
            // {
            // }
            Debug.Log("Spider has entered Patrol State");
            _spiderController.SetRandomPatrolTarget();
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            _timer += Time.deltaTime;

            if(!Mathf.Approximately(Mathf.Floor(_timer), Mathf.Floor(_timer - Time.deltaTime)))
            {
                Debug.Log("Spider is Patrolling...");
            }

            // Example transition: after idleDuration, go patrol
            if (_timer >= _spiderController.PatrolDuration)
            {
                return _spiderController.GetState(SpiderController.EnemyEnum.Chase);
            }

            if (_spiderController.TryFindNearestPlayerWithinRadius())
            {
                return _spiderController.GetState(SpiderController.EnemyEnum.Chase);
            }

            if (_spiderController.TargetTransform == null || _spiderController.DistanceToTarget <= 1f)
            {
                FindNewPatrolTarget();
            }

            return this;
        }

        private void FindNewPatrolTarget()
        {
            // Find a new patrol target
            var currentTransform = _spiderController.TargetTransform;
            var patrolPoints = _spiderController.PatrolPoints;

            if (patrolPoints != null && patrolPoints.Count > 0)
            {
                if (patrolPoints.Count == 1)
                {
                    // If there's only one patrol point, just stay there
                    _spiderController.SetTarget(patrolPoints[0]);
                }
                else
                {
                    // Create a list of available patrol points (excluding current target)
                    var availablePoints = new List<Transform>();
                    foreach (var point in patrolPoints)
                    {
                        if (point != currentTransform)
                        {
                            availablePoints.Add(point);
                        }
                    }

                    // Pick a random point from available options
                    if (availablePoints.Count > 0)
                    {
                        int randomIndex = Random.Range(0, availablePoints.Count);
                        Transform newTarget = availablePoints[randomIndex];
                        _spiderController.SetTarget(newTarget);
                    }
                }
            }
        }

        public override void Exit(MonoStateMachine controller)
        {
            // cleanup if needed
        }
    }
}