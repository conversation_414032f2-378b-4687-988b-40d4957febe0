// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyChaseState : EnemyState
    {
        private float _timer;
        private bool _isGettingBored;

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);
            _timer = 0f;
            _isGettingBored = false;
            _spiderController.FindNearestPlayer();
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_spiderController.DistanceToTarget <= _spiderController.AttackDistance)
            {
                return _spiderController.GetState(SpiderController.EnemyEnum.Attack);
            }

            if (_spiderController.DistanceToTarget >= _spiderController.LoseSightDistance)
            {
                _isGettingBored = true;
            }

            if (_isGettingBored)
            {
                _timer += Time.deltaTime;
                if (_timer >= _spiderController.BoredDuration)
                {
                    return _spiderController.GetState(SpiderController.EnemyEnum.Patrol);
                }
            }

            // If damage has been done, retreat. Are we having damage be inflicted via the cart warding?


            // stay in Chase
            return this;
        }

        public override void Exit(MonoStateMachine controller)
        {

        }
    }
}