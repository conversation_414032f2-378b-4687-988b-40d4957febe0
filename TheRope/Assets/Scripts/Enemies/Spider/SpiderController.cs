// Copyright Isto Inc.

using Isto.Core.StateMachine;
using RootMotion.Demos;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class SpiderController : MonoStateMachine
    {
        // UNITY HOOKUP

        [Header("Spider Settings")]
        [SerializeField] private MechSpider _mechSpider;
        [SerializeField] private float _speed     = 6f;
        [SerializeField] private float _turnSpeed = 60f;
        [SerializeField] private bool _isDebugging = false;

        [Header("State Setup")]
        [SerializeField] private EnemyIdleState _idleState;
        [SerializeField] private EnemyPatrolState _patrolState;
        [SerializeField] private EnemyChaseState _chaseState;
        [SerializeField] private EnemyAttackState _attackState;
        [SerializeField] private EnemyRetreatState _retreatState;
        [SerializeField] private EnemyHealState _healState;
        [SerializeField] private EnemyDeadState _deadState;

        [Head<PERSON>("Spider Variables")]
        [Tooltip("Which tag to look for when hunting players")]
        [SerializeField] private string _playerTag = "Player";
        [Toolt<PERSON>("How far the spider can 'see'")]
        [SerializeField] private float _detectionRadius = 60;
        [SerializeField] private float _loseSightDistance = 85f;
        [SerializeField] private float _attackDistance    = 5f;
        [SerializeField] private float _boredDuration = 10f;
        [SerializeField] private float _idleDuration = 5f;
        [SerializeField] private float _patrolDuration = 45f;
        [SerializeField] private Transform _patrolPointContainer;

        public enum EnemyEnum
        {
            Idle,
            Patrol,
            Chase,
            Attack,
            Retreat,
            Heal,
            Dead
        }

        // map from enum → MonoState component
        private Dictionary<EnemyEnum, MonoState> _stateMap;
        private List<Transform> _patrolPoints;

        private Transform _target;
        public Transform TargetTransform => _target;

        public bool IsDebugging => _isDebugging;
        public float DistanceToTarget => Vector3.Distance(transform.position, _target.position);
        public float LoseSightDistance => _loseSightDistance;
        public float AttackDistance => _attackDistance;
        public float BoredDuration => _boredDuration;
        public float IdleDuration => _idleDuration;
        public float PatrolDuration => _patrolDuration;
        public List<Transform> PatrolPoints => _patrolPoints;


        // UNITY LIFECYCLE

        // build the map once
        protected void Awake()
        {
            _stateMap = new Dictionary<EnemyEnum, MonoState>
            {
                { EnemyEnum.Idle,    _idleState   },
                { EnemyEnum.Patrol,  _patrolState },
                { EnemyEnum.Chase,   _chaseState  },
                { EnemyEnum.Attack,  _attackState },
                { EnemyEnum.Retreat, _retreatState},
                { EnemyEnum.Heal,    _healState   },
                { EnemyEnum.Dead,    _deadState   },
            };

            _patrolPoints = new List<Transform>();
            foreach (Transform child in _patrolPointContainer)
            {
                _patrolPoints.Add(child);
            }
        }

        // run the state‐machine first, then do movement
        protected override void Update()
        {
            if (IsDebugging)
            {
                DrawDetectionRays();
            }

            base.Update();

            // 3) Do movement as before
            if(_target == null)
            {
                return;
            }

            Vector3 dir = (_target.position - transform.position).normalized;
            if (dir.sqrMagnitude > 0f)
            {
                Quaternion lookRot = Quaternion.LookRotation(dir);
                transform.rotation = Quaternion.RotateTowards(
                    transform.rotation,
                    lookRot,
                    Time.deltaTime * _turnSpeed
                );
                transform.Translate(
                    Vector3.forward * _speed * Time.deltaTime * _mechSpider.scale,
                    Space.Self
                );
            }
        }

        public void ChangeState(EnemyEnum newState)
        {
            Debug.Log("Spider changing state to " + newState + " from " + _currentState.GetType().Name);
            ChangeState(_stateMap[newState]);
        }

        public MonoState GetState(EnemyEnum newState)
        {
            return _stateMap[newState];
        }

        /// <summary>
        /// Scans all GameObjects with tag=playerTag, picks the closest one within detectionRadius.
        /// </summary>
        public bool TryFindNearestPlayerWithinRadius()
        {
            var players = GameObject.FindGameObjectsWithTag(_playerTag);
            Transform best = null;
            float bestSqr = _detectionRadius * _detectionRadius;
            Vector3 myPos = transform.position;

            foreach (var go in players)
            {
                float d2 = (go.transform.position - myPos).sqrMagnitude;
                if (d2 <= bestSqr)
                {
                    bestSqr = d2;
                    best    = go.transform;
                }
            }
            _target = best;
            return best != null;
        }

        public void FindNearestPlayer()
        {
            var players = GameObject.FindGameObjectsWithTag(_playerTag);
            Transform best = null;
            float bestSqr = float.MaxValue;
            Vector3 myPos = transform.position;

            foreach (var go in players)
            {
                float d2 = (go.transform.position - myPos).sqrMagnitude;
                if (d2 < bestSqr)
                {
                    bestSqr = d2;
                    best    = go.transform;
                }
            }
            _target = best;
        }

        public void SetTarget(Transform newTarget)
        {
            _target = newTarget;
        }

        public void SetRandomPatrolTarget()
        {
            if (_patrolPoints != null && _patrolPoints.Count > 0)
            {
                int randomIndex = Random.Range(0, _patrolPoints.Count);
                _target = _patrolPoints[randomIndex];
            }
        }

        private void DrawDetectionRays()
        {
            int rayCount = 36;
            float step = 360f / rayCount;

            for (int i = 0; i < rayCount; i++)
            {
                float angle = step * i * Mathf.Deg2Rad;
                Vector3 dir = new Vector3(Mathf.Cos(angle), 0f, Mathf.Sin(angle));
                Debug.DrawRay(
                    transform.position,
                    dir * _detectionRadius,
                    Color.red
                );
            }
        }

#if UNITY_EDITOR
        // Draw a wireframe sphere in the editor
        private void OnDrawGizmosSelected()
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, _detectionRadius);
        }
#endif
    }
}